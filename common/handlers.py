import logging
from aiogram import Router, F
from aiogram.types import CallbackQuery, Message
from aiogram.fsm.context import FSMContext
from aiogram.filters import Command

router = Router()

# Импортируем менеджер навигации
from common.register_handlers_and_transitions import navigation_manager

@router.callback_query(F.data == "back")
async def go_back(callback: CallbackQuery, state: FSMContext, user_role: str = None):
    await navigation_manager.handle_back(callback, state, user_role)

# Регистрация обработчика кнопки "Главное меню"
@router.callback_query(F.data == "back_to_main")
async def back_to_main_handler(callback: CallbackQuery, state: FSMContext, user_role: str):
    await navigation_manager.handle_main_menu(callback, state, user_role)

# Обработчики кнопок постоянной клавиатуры
@router.message(F.text == "ученик")
async def keyboard_student_handler(message: Message, state: FSMContext, user_role: str = None):
    """Обработчик кнопки 'ученик' с клавиатуры"""
    from student.handlers.main import show_student_main_menu
    await show_student_main_menu(message, user_role=user_role)

@router.message(F.text == "админ")
async def keyboard_admin_handler(message: Message, state: FSMContext, user_role: str = None):
    """Обработчик кнопки 'админ' с клавиатуры"""
    if user_role == "admin":
        from admin.handlers.main import show_admin_main_menu
        await show_admin_main_menu(message, user_role=user_role)

@router.message(F.text == "менеджер")
async def keyboard_manager_handler(message: Message, state: FSMContext, user_role: str = None):
    """Обработчик кнопки 'менеджер' с клавиатуры"""
    if user_role in ["admin", "manager"]:
        from manager.handlers.main import show_manager_main_menu
        await show_manager_main_menu(message, user_role=user_role)

@router.message(F.text == "преподаватель")
async def keyboard_teacher_handler(message: Message, state: FSMContext, user_role: str = None):
    """Обработчик кнопки 'преподаватель' с клавиатуры"""
    if user_role in ["admin", "teacher"]:
        from teacher.handlers.main import show_teacher_main_menu
        await show_teacher_main_menu(message, user_role=user_role)

@router.message(F.text == "куратор")
async def keyboard_curator_handler(message: Message, state: FSMContext, user_role: str = None):
    """Обработчик кнопки 'куратор' с клавиатуры"""
    if user_role in ["admin", "curator"]:
        from curator.handlers.main import show_curator_main_menu
        await show_curator_main_menu(message, user_role=user_role)

@router.message(F.text == "старт")
async def keyboard_start_handler(message: Message, state: FSMContext, user_role: str = None):
    """Обработчик кнопки 'старт' с клавиатуры"""
    # Перенаправляем на команду /start
    if user_role == "admin":
        from admin.handlers.main import show_admin_main_menu
        await show_admin_main_menu(message, user_role=user_role)
    elif user_role == "manager":
        from manager.handlers.main import show_manager_main_menu
        await show_manager_main_menu(message, user_role=user_role)
    elif user_role == "curator":
        from curator.handlers.main import show_curator_main_menu
        await show_curator_main_menu(message, user_role=user_role)
    elif user_role == "teacher":
        from teacher.handlers.main import show_teacher_main_menu
        await show_teacher_main_menu(message, user_role=user_role)
    else:  # По умолчанию считаем пользователя студентом
        from student.handlers.main import show_student_main_menu
        await show_student_main_menu(message, user_role=user_role)

@router.message(Command("id"))
async def id_command_handler(message: Message, user_role: str = None):
    """Обработчик команды /id - отправляет пользователю его Telegram ID"""
    logging.info(f"ВЫЗОВ: id_command_handler | РОЛЬ: {user_role}")

    user_id = message.from_user.id
    username = message.from_user.username
    first_name = message.from_user.first_name or ""
    last_name = message.from_user.last_name or ""

    # Формируем полное имя
    full_name = f"{first_name} {last_name}".strip()

    # Формируем текст сообщения
    text = f"🆔 <b>Ваш Telegram ID:</b>\n\n<code>{user_id}</code>\n\n"

    if username:
        text += f"👤 <b>Username:</b> @{username}\n"

    if full_name:
        text += f"📝 <b>Имя:</b> {full_name}\n"

    text += "\n💡 <i>Нажмите на ID чтобы скопировать</i>"

    await message.answer(text)
